import { useState, useEffect, useCallback } from 'react';
import authService from '../services/authService';

const useCurrentUser = () => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get profile picture URL
  const getProfilePictureUrl = useCallback((profilePicture) => {
    if (!profilePicture) return null;
    
    // If it's already a full URL, return as is
    if (profilePicture.startsWith('http')) {
      return profilePicture;
    }
    
    // Construct full URL for backend served images
    return `http://127.0.0.1:8000${profilePicture}`;
  }, []);

  // Fetch current user from API
  const fetchCurrentUser = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const userData = await authService.getCurrentUser();
      
      if (userData) {
        // Enhance user data with profile picture URL
        const enhancedUser = {
          ...userData,
          profilePictureUrl: getProfilePictureUrl(userData.profile_picture)
        };
        
        setCurrentUser(enhancedUser);
        
        // Update session storage with fresh data
        sessionStorage.setItem('current_user', JSON.stringify(enhancedUser));
      } else {
        setCurrentUser(null);
        sessionStorage.removeItem('current_user');
      }
    } catch (err) {
      console.error('Failed to fetch current user:', err);
      setError(err);
      setCurrentUser(null);
    } finally {
      setIsLoading(false);
    }
  }, [getProfilePictureUrl]);

  // Initialize user from session storage or fetch from API
  useEffect(() => {
    const initializeUser = async () => {
      try {
        // First, try to get user from session storage
        const storedUser = sessionStorage.getItem('current_user');
        
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          
          // Enhance with profile picture URL if not already present
          if (!parsedUser.profilePictureUrl && parsedUser.profile_picture) {
            parsedUser.profilePictureUrl = getProfilePictureUrl(parsedUser.profile_picture);
          }
          
          setCurrentUser(parsedUser);
          setIsLoading(false);
          
          // Optionally refresh user data in background
          fetchCurrentUser();
        } else {
          // No stored user, fetch from API
          await fetchCurrentUser();
        }
      } catch (err) {
        console.error('Failed to initialize user:', err);
        setError(err);
        setIsLoading(false);
      }
    };

    initializeUser();
  }, [fetchCurrentUser, getProfilePictureUrl]);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    await fetchCurrentUser();
  }, [fetchCurrentUser]);

  // Update user data (for profile updates)
  const updateUser = useCallback((updatedData) => {
    if (currentUser) {
      const updatedUser = {
        ...currentUser,
        ...updatedData,
        profilePictureUrl: updatedData.profile_picture 
          ? getProfilePictureUrl(updatedData.profile_picture)
          : currentUser.profilePictureUrl
      };
      
      setCurrentUser(updatedUser);
      sessionStorage.setItem('current_user', JSON.stringify(updatedUser));
    }
  }, [currentUser, getProfilePictureUrl]);

  // Clear user data (for logout)
  const clearUser = useCallback(() => {
    setCurrentUser(null);
    sessionStorage.removeItem('current_user');
  }, []);

  return {
    currentUser,
    isLoading,
    error,
    refreshUser,
    updateUser,
    clearUser,
    getProfilePictureUrl
  };
};

export default useCurrentUser;
