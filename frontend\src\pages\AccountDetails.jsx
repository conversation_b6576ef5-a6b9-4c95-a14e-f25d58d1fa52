import Navbar from "../components/NavBar";
import "../css/AccountDetails.css";
import SampleProfile from "../assets/img/do.png";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import accountsService from "../services/accountsService";
import RippleLoading from "../components/modals/loading/rippleLoading";
import Alert from "../components/modals/Alert";
import authService from "../services/authService";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

export default function AccountDetails() {
  const [userInfo, setUserInfo] = useState(null);
  const [isUpdateSuccess, setUpdateSuccess] = useState(false);
  const [isSubmitting, setSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [file, setFile] = useState(null);
  const [changeProfileResStatus, setChangeProfileResStatus] = useState(null);
  const [isSubmittingProfile, setSubmittingProfile] = useState(false);

  const maskEmail = (email) => {
    const [user, domain] = email.split("@");
    const maskedUser = user[0] + "*".repeat(user.length - 1);
    return `${maskedUser}@${domain}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const handleFileUpload = (event) => {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      setFile(file);
    } else {
      setFile(null);
    }
  };

  const { register, handleSubmit } = useForm({
    mode: "all",
  });

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const currentUser = await authService.getCurrentUser();
        if (currentUser) {
          setUserInfo({
            id: currentUser.id,
            firstName: currentUser.first_name,
            lastName: currentUser.last_name,
            email: currentUser.email,
            is_superuser: currentUser.is_superuser,
            profile: currentUser.profile_picture,
            dateCreated: currentUser.date_created,
            dateUpdated: currentUser.date_updated,
            isActive: currentUser.is_active,
          });
          // Update session storage with fresh data
          sessionStorage.setItem("current_user", JSON.stringify(currentUser));
        }
      } catch (error) {
        console.log("Failed to fetch user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const submission = async (data) => {
    setSubmitting(true);

    console.log("data:", data);
    try {
      const response = await accountsService.updateUserInfo(
        userInfo.id,
        data.firstName,
        data.lastName
      );

      console.log("response received:", response);

      if (!response === 200) {
        console.log("failed to update user info", response);
        setUpdateSuccess(false);
      } else {
        console.log("going to update user info...");
        // Update the current user stored in the session storage
        await updateUserInfo();
        setUpdateSuccess(true);
      }
    } catch (error) {
      console.log("Failed to update user info!", error);
    } finally {
      setSubmitting(false);
    }
  };

  const changeProfileSubmission = async (userId, file) => {
    setSubmittingProfile(true);
    const response = await accountsService.updateProfile(userId, file);

    if (!response === 200) {
      console.log("failed to update user profile picture", response);
      setChangeProfileResStatus(response);
    } else {
      console.log("going to update user profile picture...");
      // Update the current user stored in the session storage
      updateUserInfo();
      setChangeProfileResStatus(response);
      setSubmittingProfile(false);
    }
  };

  const updateUserInfo = async () => {
    try {
      const getCurrentUser = await authService.getCurrentUser();
      if (getCurrentUser) {
        setUserInfo({
          id: getCurrentUser.id,
          firstName: getCurrentUser.first_name,
          lastName: getCurrentUser.last_name,
          email: getCurrentUser.email,
          is_superuser: getCurrentUser.is_superuser,
          profile: getCurrentUser.profile_picture,
          dateCreated: getCurrentUser.date_created,
          dateUpdated: getCurrentUser.date_updated,
          isActive: getCurrentUser.is_active,
        });
        sessionStorage.setItem("current_user", JSON.stringify(getCurrentUser));
      }
    } catch (error) {
      console.log("Failed to update user info:", error);
    }
  };

  useEffect(() => {
    if (isUpdateSuccess) {
      setTimeout(() => {
        setUpdateSuccess(false);
      }, 5000);
    }
  }, [isUpdateSuccess]);

  return (
    <>
      {isUpdateSuccess && (
        <Alert message={"Successfully Updated!"} type={"success"} />
      )}

      {changeProfileResStatus === 200 && (
        <Alert
          message={"Successfully changed profile picture!"}
          type={"success"}
        />
      )}

      <Navbar />
      <main className="account-details">
        <section>
          <div className="title-page">
            <h1>Account Profile</h1>
            <div className="profile-container">
              <h4>
                {isLoading || !userInfo ? (
                  <Skeleton height={20} width={100} />
                ) : (
                  userInfo.firstName + " " + userInfo.lastName
                )}
              </h4>
              <img
                src={userInfo?.profile ? userInfo.profile : SampleProfile}
                alt=""
              />
            </div>
          </div>
        </section>
        <section>
          <section className="left-panel">
            <img
              src={userInfo?.profile ? userInfo.profile : SampleProfile}
              alt=""
            />

            <input
              type="file"
              name="profile"
              id="profile"
              style={{ display: "none" }}
              accept="image/*"
              onChange={handleFileUpload}
            />

            {file === null && (
              <label htmlFor="profile">Change Profile Picture</label>
            )}

            {file != null && (
              <button
                onClick={() => changeProfileSubmission(userInfo?.id, file)}
                disabled={isSubmittingProfile}
              >
                {isSubmittingProfile && <RippleLoading />}
                Save Profile Change
              </button>
            )}

            <form action={handleSubmit(submission)}>
              <fieldset>
                <label htmlFor="first-name">First name</label>
                {isLoading || !userInfo ? (
                  <Skeleton height={40} width={200} borderRadius={40} />
                ) : (
                  <input
                    type="text"
                    name="first-name"
                    id="first-name"
                    defaultValue={userInfo.firstName}
                    {...register("firstName")}
                  />
                )}
              </fieldset>
              <fieldset>
                <label htmlFor="last-name">Last name</label>
                {isLoading || !userInfo ? (
                  <Skeleton height={40} width={200} borderRadius={40} />
                ) : (
                  <input
                    type="text"
                    name="last-name"
                    id="last-name"
                    defaultValue={userInfo.lastName}
                    {...register("lastName")}
                  />
                )}
              </fieldset>
              <button
                type="submit"
                className="submit-button"
                disabled={isSubmitting || !userInfo}
              >
                {isSubmitting && <RippleLoading />}
                Save Changes
              </button>
            </form>
          </section>
          <section className="right-panel">
            <h2>Account Overview</h2>
            <fieldset>
              <label htmlFor="email">Email:</label>
              {isLoading || !userInfo ? (
                <Skeleton height={40} width={200} borderRadius={40} />
              ) : (
                <input
                  type="text"
                  name="email"
                  id="email"
                  value={maskEmail(userInfo.email)}
                  disabled
                />
              )}
            </fieldset>
            <fieldset>
              <label htmlFor="role">Role:</label>
              {isLoading || !userInfo ? (
                <Skeleton height={40} width={200} borderRadius={40} />
              ) : (
                <input
                  type="text"
                  name="role"
                  id="role"
                  value={userInfo.is_superuser ? "Admin" : "Operator"}
                  disabled
                />
              )}
            </fieldset>
            <fieldset>
              <label htmlFor="status">Status:</label>
              {isLoading || !userInfo ? (
                <Skeleton height={40} width={200} borderRadius={40} />
              ) : (
                <input
                  type="text"
                  name="status"
                  id="status"
                  value={userInfo.isActive ? "Active" : "Inactive"}
                  disabled
                />
              )}
            </fieldset>
            <fieldset>
              <label htmlFor="date-created">Date Created:</label>
              {isLoading || !userInfo ? (
                <Skeleton height={40} width={200} borderRadius={40} />
              ) : (
                <input
                  type="text"
                  name="date-created"
                  id="date-created"
                  value={formatDate(userInfo.dateCreated)}
                  disabled
                />
              )}
            </fieldset>
            <fieldset>
              <label htmlFor="date-updated">Last Updated:</label>
              {isLoading || !userInfo ? (
                <Skeleton height={40} width={200} borderRadius={40} />
              ) : (
                <input
                  type="text"
                  name="date-updated"
                  id="date-updated"
                  value={formatDate(userInfo.dateUpdated)}
                  disabled
                />
              )}
            </fieldset>
          </section>
        </section>
      </main>
    </>
  );
}
