{"name": "react-tooltip", "version": "5.28.1", "description": "react tooltip component", "scripts": {"dev-rollup": "node ./prebuild.js --env=development && node --max_old_space_size=2048 ./node_modules/rollup/dist/bin/rollup -c rollup.config.dev.js --watch", "build": "node ./prebuild.js --env=production && npm run types && node --max_old_space_size=2048 ./node_modules/rollup/dist/bin/rollup -c rollup.config.prod.js", "dev": "node ./prebuild.js --env=development && node ./esbuild.config.dev.mjs", "build-esbuild": "node ./prebuild.js --env=production && node ./esbuild.config.prod.mjs", "types": "node --max_old_space_size=2048 ./node_modules/rollup/dist/bin/rollup -c rollup.config.types.js", "eslint": "eslint --ext=js --ext=jsx --ext=ts --ext=tsx --fix ./src", "stylelint": "stylelint \"src/**/*.css\"", "prettier": "prettier --config ./.prettierrc.json --write \"src/**/*{.js,.jsx,.ts,.tsx,.css}\"", "prepare": "husky install", "bundlesize": "bundlesize", "esbuild": "esbuild", "test": "jest", "postbuild": "npm run types && npm run bundlesize", "prepublishOnly": "npm run build"}, "types": "dist/react-tooltip.d.ts", "license": "MIT", "private": false, "author": "ReactTooltip", "repository": "https://github.com/ReactTooltip/react-tooltip", "keywords": ["react", "react-component", "tooltip", "react-tooltip"], "bugs": {"url": "https://github.com/ReactTooltip/react-tooltip/issues"}, "main": "dist/react-tooltip.min.cjs", "module": "dist/react-tooltip.min.mjs", "exports": {".": {"types": "./dist/react-tooltip.d.ts", "require": "./dist/react-tooltip.min.cjs", "import": "./dist/react-tooltip.min.mjs", "default": "./dist/react-tooltip.min.cjs"}, "./dist/react-tooltip": "./dist/react-tooltip.min.cjs", "./dist/react-tooltip.css": "./dist/react-tooltip.min.css", "./dist/react-tooltip.d.ts": "./dist/react-tooltip.d.ts", "./package.json": "./package.json"}, "homepage": "https://github.com/ReactTooltip/react-tooltip#readme", "devDependencies": {"@rollup/plugin-commonjs": "22.0.2", "@rollup/plugin-node-resolve": "14.1.0", "@rollup/plugin-replace": "4.0.0", "@rollup/plugin-typescript": "8.5.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "12.1.5", "@testing-library/user-event": "^14.4.3", "@types/css": "^0.0.33", "@types/css-modules": "^1.0.2", "@types/jest": "29.4.0", "@types/node": "^18.15.3", "@types/react": "^18.2.17", "@types/react-dom": "18.0.11", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "5.54.0", "@typescript-eslint/parser": "5.54.0", "bundlesize": "^0.18.1", "css-loader": "6.7.3", "esbuild": "0.17.11", "esbuild-css-modules-plugin": "^2.7.1", "eslint": "8.35.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.6.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "8.0.3", "jest": "29.4.3", "jest-environment-jsdom": "29.4.3", "jest-transform-css": "6.0.1", "lint-staged": "13.1.2", "minimist": "^1.2.8", "postcss": "8.4.21", "prettier": "2.8.4", "process": "^0.11.10", "react": "16.14.0", "react-dom": "16.14.0", "rimraf": "^3.0.2", "rollup": "2.79.1", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-browsersync": "^1.3.3", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-dts": "5.2.0", "rollup-plugin-filesize": "^9.1.1", "rollup-plugin-html-scaffold": "^0.2.0", "rollup-plugin-postcss": "^4.0.1", "rollup-plugin-progress": "^1.1.2", "rollup-plugin-string": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "5.9.0", "style-loader": "^3.3.0", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^22.0.0", "ts-jest": "29.0.5", "ts-node": "^10.9.1", "typescript": "4.9.5"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@floating-ui/dom": "^1.6.1", "classnames": "^2.3.0"}}