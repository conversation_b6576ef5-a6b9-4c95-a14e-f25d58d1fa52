.account-details {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  padding: 90px 38px 20px 38px;

  background-color: var(--primary-color);
}

.account-details .title-page {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  padding: 10px;
  border-radius: 40px;

  background-color: white;
  box-shadow: 0 4px 20px 2px rgb(211, 211, 211, 0.5);
}

.account-details .title-page h1 {
  margin-left: 20px;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--secondary-dark-color);
}

.account-details h2 {
  color: var(--secondary-dark-color);
}

.account-details .title-page h4 {
  font-size: 0.875rem;
  color: var(--secondary-color);
}

.account-details .title-page .profile-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  padding: 10px;
  border-radius: 40px;

  gap: 10px;

  background-color: var(--primary-dark-color);
  box-shadow: 0 4px 20px 2px rgb(211, 211, 211, 0.5);
}

.account-details .title-page img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  object-fit: cover;
  object-position: center;

  box-shadow: 0 0 0 1px var(--primary-dark-color),
    0 0 0 2px var(--primary-color);

  transition: ease 0.5s;
}

.account-details section:nth-last-child(1) {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: start;
  width: 100%;

  gap: 20px;
}

.account-details section:nth-last-child(1) section:not(.left-panel) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  width: 100%;
  gap: 20px;
}

.account-details .left-panel,
.account-details .right-panel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;

  padding: 20px;
  border-radius: 40px;

  background-color: white;
  border: 1px solid #d3d3d3;

  gap: 20px;
}

.account-details .left-panel form:nth-of-type(1) {
  display: flex;
  align-items: center;
}

.account-details .right-panel fieldset input {
  background-color: rgb(211, 211, 211, 0.5);
  color: rgb(51, 65, 85, 0.5);
}

.account-details .left-panel img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  object-position: center;

  box-shadow: 0 0 0 2px var(--primary-color), 0 0 0 4px var(--btn-color);

  transition: ease 0.5s;
}

.account-details .left-panel button:nth-last-child(2),
.account-details .left-panel label:nth-of-type(1):not(fieldset label) {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  width: fit-content;

  border-radius: 40px;
  background-color: transparent;
  border: 1px solid var(--btn-color);
  color: var(--btn-color);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;

  transition: ease 0.5s;
}

.account-details .left-panel button:nth-last-child(2):hover,
.account-details .left-panel label:nth-of-type(1):not(fieldset label):hover {
  background-color: var(--btn-color);
  color: var(--primary-color);
}

.account-details .right-panel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;

  padding: 20px;
  border-radius: 40px;

  background-color: white;
  border: 1px solid #d3d3d3;

  gap: 20px;
}
